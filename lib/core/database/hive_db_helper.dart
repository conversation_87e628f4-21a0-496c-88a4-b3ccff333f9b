import 'package:hive/hive.dart';

import '../database/entities/local_user_entity.dart';

class HiveUserService {
  static final HiveUserService _instance = HiveUserService._internal();
  static const String _boxName = 'usersBox';
  late Box _box;

  HiveUserService._internal();

  factory HiveUserService() => _instance;

  Future<void> init() async {
    _box = await Hive.openBox(_boxName);
  }

  Future<bool> saveUserAfterLogin(dynamic loginModel) async {
    try {
      final localUser = LocalUserEntity.fromLoginModel(loginModel);
      await _box.put(localUser.id, localUser.toMap());
      return true;
    } catch (e) {
      print('Error saving user after login: $e');
      return false;
    }
  }

  Future<bool> saveUserAfterSignup(dynamic signupModel) async {
    try {
      final localUser = LocalUserEntity.fromSignupModel(signupModel);
      await _box.put(localUser.id, localUser.toMap());
      return true;
    } catch (e) {
      print('Error saving user after signup: $e');
      return false;
    }
  }

  Future<LocalUserEntity?> getCurrentUser() async {
    try {
      if (_box.isEmpty) {
        print('Box is empty, no current user');
        return null;
      }

      final users = <LocalUserEntity>[];

      // Safely iterate through box values
      for (final value in _box.values) {
        try {
          if (value != null) {
            final userMap = Map<String, dynamic>.from(value);
            final user = LocalUserEntity.fromMap(userMap);
            users.add(user);
          }
        } catch (e) {
          print('Error parsing user data: $e');
          // Skip this user and continue with others
          continue;
        }
      }

      if (users.isEmpty) {
        print('No valid users found');
        return null;
      }

      // Sort users by last updated time
      users.sort((a, b) => b.localUpdatedAt.compareTo(a.localUpdatedAt));
      final user = users.first;

      // Check token expiration with null safety
      final now = DateTime.now();
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        (user.expiresAt ?? 0) * 1000,
      );

      if (now.isAfter(expiresAt)) {
        print('User token has expired');
        return null;
      }

      print('Current user retrieved successfully: ${user.id}');
      return user;
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  Future<LocalUserEntity?> getUserById(String id) async {
    try {
      final data = _box.get(id);
      if (data == null) return null;

      final userMap = Map<String, dynamic>.from(data);
      return LocalUserEntity.fromMap(userMap);
    } catch (e) {
      print('Error getting user by ID: $e');
      return null;
    }
  }

  Future<List<LocalUserEntity>> getAllUsers() async {
    try {
      final users = <LocalUserEntity>[];

      for (final value in _box.values) {
        try {
          if (value != null) {
            final userMap = Map<String, dynamic>.from(value);
            final user = LocalUserEntity.fromMap(userMap);
            users.add(user);
          }
        } catch (e) {
          print('Error parsing user data in getAllUsers: $e');
          continue;
        }
      }

      return users;
    } catch (e) {
      print('Error getting all users: $e');
      return [];
    }
  }

  Future<bool> updateUser(LocalUserEntity user) async {
    try {
      await _box.put(user.id, user.toMap());
      return true;
    } catch (e) {
      print('Error updating user: $e');
      return false;
    }
  }

  Future<bool> deleteUser(String userId) async {
    try {
      await _box.delete(userId);
      return true;
    } catch (e) {
      print('Error deleting user: $e');
      return false;
    }
  }

  Future<bool> deleteAllUsers() async {
    try {
      await _box.clear();
      return true;
    } catch (e) {
      print('Error deleting all users: $e');
      return false;
    }
  }

  Future<bool> userExists(String userId) async {
    try {
      return _box.containsKey(userId);
    } catch (e) {
      print('Error checking if user exists: $e');
      return false;
    }
  }

  Future<int> getUserCount() async {
    try {
      return _box.length;
    } catch (e) {
      print('Error getting user count: $e');
      return 0;
    }
  }

  /// Check if user is logged in and token is still valid
  Future<bool> isLoggedIn() async {
    try {
      if (_box.isEmpty) return false;

      final users = <LocalUserEntity>[];

      for (final value in _box.values) {
        try {
          if (value != null) {
            final userMap = Map<String, dynamic>.from(value);
            final user = LocalUserEntity.fromMap(userMap);
            users.add(user);
          }
        } catch (e) {
          print('Error parsing user data in isLoggedIn: $e');
          continue;
        }
      }

      if (users.isEmpty) return false;

      final user = users.first;
      final now = DateTime.now();
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        (user.expiresAt ?? 0) * 1000,
      );

      return now.isBefore(expiresAt);
    } catch (e) {
      print('Error checking login status: $e');
      return false;
    }
  }

  /// Logout user by clearing all user data
  Future<bool> logout() async {
    try {
      await _box.clear();
      return true;
    } catch (e) {
      print('Error during logout: $e');
      return false;
    }
  }

  /// Save user info from fn_get_user_info API response
  Future<bool> saveUserInfo(Map<String, dynamic> userInfo) async {
    try {
      await _box.put('user_info', userInfo);
      print('User info saved successfully');
      return true;
    } catch (e) {
      print('Error saving user info: $e');
      return false;
    }
  }

  /// Get user info from local storage
  Future<Map<String, dynamic>?> getUserInfo() async {
    try {
      final data = _box.get('user_info');
      if (data == null) {
        print('No user info found in local storage');
        return null;
      }

      final userInfo = Map<String, dynamic>.from(data);
      print('User info retrieved from local storage: $userInfo');
      return userInfo;
    } catch (e) {
      print('Error getting user info: $e');
      return null;
    }
  }

  /// Clear corrupted data and reset box if needed
  Future<bool> clearCorruptedData() async {
    try {
      print('Clearing potentially corrupted data...');

      // Get all keys
      final keys = _box.keys.toList();

      // Remove corrupted entries
      for (final key in keys) {
        try {
          final value = _box.get(key);
          if (value != null && key != 'user_info') {
            // Try to parse as LocalUserEntity
            final userMap = Map<String, dynamic>.from(value);
            LocalUserEntity.fromMap(userMap);
          }
        } catch (e) {
          print('Removing corrupted entry with key: $key');
          await _box.delete(key);
        }
      }

      print('Corrupted data cleanup completed');
      return true;
    } catch (e) {
      print('Error clearing corrupted data: $e');
      return false;
    }
  }
}
