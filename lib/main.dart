import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'features/auth/presentation/bloc/auth/auth_bloc.dart';
import 'features/exercises/presentation/bloc/exercises/exercises_bloc.dart';
import 'features/mood/presentation/bloc/mood/mood_bloc.dart';
import 'features/profile/presentation/bloc/profile/profile_bloc.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Supabase with environment variables
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.env['SUPABASE_API_KEY']!,
  );

  // Initialize SQLite database
  // Only initialize SQLite database on non-web platforms
  // if (!kIsWeb) {
  //   final databaseHelper = DatabaseHelper();
  //   await databaseHelper
  //       .database; // This will create the database if it doesn't exist
  // }

  WidgetsFlutterBinding.ensureInitialized();

  if (kIsWeb) {
    Hive.init('hive_boxes'); // Web storage
  } else {
    final appDocumentDir = await getApplicationDocumentsDirectory();
    Hive.init(appDocumentDir.path); // Native platform storage
  }

  await HiveUserService().init();

  runApp(const RecallLoopApp());
}

class RecallLoopApp extends StatelessWidget {
  const RecallLoopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      // designSize: const Size(375, 812), // iPhone 12 Pro design size
      designSize: const Size(1440, 900), // Desktop design size
      // designSize: const Size(768, 1024), // tablet design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => AuthBloc()),
            BlocProvider(create: (context) => ProfileBloc()),
            BlocProvider(create: (context) => ExercisesBloc()),
            BlocProvider(create: (context) => MoodBloc()),
          ],
          child: MaterialApp.router(
            title: 'RecallLoop',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
          ),
        );
      },
    );
  }
}
