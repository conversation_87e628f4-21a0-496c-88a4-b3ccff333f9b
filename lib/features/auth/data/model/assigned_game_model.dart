class AssignedGameInfo {
  final String? gameId;
  final String? gameName;
  final String? categoryName;
  final String? thumbnailUrl;
  final String? description;
  final int? difficultyLevel;
  final bool? isActive;
  final int? noOfLevels;

  AssignedGameInfo({
    this.gameId,
    this.gameName,
    this.categoryName,
    this.thumbnailUrl,
    this.description,
    this.difficultyLevel,
    this.isActive,
    this.noOfLevels,
  });

  factory AssignedGameInfo.fromJson(Map<String, dynamic> json) {
    return AssignedGameInfo(
      gameId: json['game_id'],
      gameName: json['game_name'],
      categoryName: json['category_name'],
      thumbnailUrl: json['thumbnail_url'],
      description: json['description'],
      difficultyLevel: json['difficulty_level'],
      isActive: json['is_active'],
      noOfLevels: json['no_of_levels'],
    );
  }
}
