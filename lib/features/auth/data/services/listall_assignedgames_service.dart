import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:recallloop/core/urls/api_config.dart';
import 'package:recallloop/features/auth/data/model/assigned_game_model.dart';

class AssignedGamesInfoService {
  static Future<List<AssignedGameInfo>?> getAssignedGames({
    // required String tenantId,
    required String tenantCode,
    required String partyId,
    required String partyTypeKey,
    String? accessToken,
  }) async {
    try {
      final url = Uri.parse(
        '${dotenv.env['SUPABASE_URL']!}/rest/v1/rpc/${ApiConfig.listAllAssignedGames}',
      );

      final headers = {
        'Content-Type': 'application/json',
        'apikey': dotenv.env['SUPABASE_API_KEY'] ?? '',
        if (accessToken != null) 'Authorization': 'Bearer $accessToken',
      };

      final body = {
        // "tenant_id": tenantId,
        "tenant_code": tenantCode,
        "party_id": partyId,
        "party_type_key": partyType<PERSON>ey,
      };

      final response = await http.post(
        url,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        print('✅ Assigned games loaded successfully: ${data.length} games');
        return data.map((json) => AssignedGameInfo.fromJson(json)).toList();
      } else {
        print(
          'Failed to load assigned games. Status: ${response.statusCode}, Response: ${response.body}',
        );
        return null;
      }
    } catch (e) {
      print('Error loading assigned games: $e');
      return null;
    }
  }
}
