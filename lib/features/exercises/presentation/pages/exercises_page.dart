import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../bloc/exercises/exercises_bloc.dart';
import '../../domain/entities/exercise.dart';

class ExercisesPage extends StatefulWidget {
  const ExercisesPage({super.key});

  @override
  State<ExercisesPage> createState() => _ExercisesPageState();
}

class _ExercisesPageState extends State<ExercisesPage> {
  @override
  void initState() {
    super.initState();
    context.read<ExercisesBloc>().add(const ExercisesEvent.loadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Cognitive Exercises',
          style: AppTheme.headlineSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExercisesList(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: _buildExercisesList(),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 1000.w),
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: _buildExercisesList(),
        ),
      ),
    );
  }

  Widget _buildExercisesList() {
    return BlocBuilder<ExercisesBloc, ExercisesState>(
      builder: (context, state) {
        return state.when(
          initial: () => const Center(child: CircularProgressIndicator()),
          loading: () => Center(
            child: CircularProgressIndicator(
              color: AppTheme.primaryColor,
            ),
          ),
          loaded: (exercises, filteredExercises, currentFilter, difficultyFilter) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppTheme.spacing24),
                _buildExerciseCategories(),
                SizedBox(height: AppTheme.spacing24),
                Expanded(
                  child: _buildExerciseGrid(filteredExercises),
                ),
              ],
            );
          },
          exerciseInProgress: (exercise) => Center(
            child: ResponsiveText(
              'Exercise in progress: ${exercise.title}',
              style: AppTheme.bodyMedium,
            ),
          ),
          error: (message) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: AppTheme.textTertiary,
                ),
                SizedBox(height: AppTheme.spacing16),
                ResponsiveText(
                  'Unable to load exercises',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                ),
                SizedBox(height: AppTheme.spacing8),
                ResponsiveText(
                  message,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textTertiary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(AppTheme.spacing20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.secondaryColor.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
          width: 1.w,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            ),
            child: Icon(
              Icons.psychology,
              color: AppTheme.primaryColor,
              size: 30.sp,
            ),
          ),
          SizedBox(width: AppTheme.spacing16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  'Train Your Brain',
                  style: AppTheme.titleLarge.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimary,
                  ),
                ),
                SizedBox(height: 4.h),
                ResponsiveText(
                  'Choose from various cognitive exercises designed to improve memory, focus, and mental agility.',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseCategories() {
    final categories = [
      {'name': 'All', 'icon': Icons.apps, 'color': AppTheme.primaryColor},
      {'name': 'Memory', 'icon': Icons.memory, 'color': AppTheme.secondaryColor},
      {'name': 'Logic', 'icon': Icons.calculate, 'color': AppTheme.accentColor},
      {'name': 'Verbal', 'icon': Icons.record_voice_over, 'color': AppTheme.warningColor},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: categories.map((category) {
          return Padding(
            padding: EdgeInsets.only(right: AppTheme.spacing12),
            child: _buildCategoryChip(
              name: category['name'] as String,
              icon: category['icon'] as IconData,
              color: category['color'] as Color,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildCategoryChip({
    required String name,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing16,
        vertical: AppTheme.spacing8,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 16.sp,
          ),
          SizedBox(width: AppTheme.spacing8),
          ResponsiveText(
            name,
            style: AppTheme.labelMedium.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseGrid(List<Exercise> exercises) {
    if (ResponsiveBreakpoints.isMobile(context)) {
      // Use ListView for mobile for better scrolling experience
      return ListView.builder(
        itemCount: exercises.length,
        itemBuilder: (context, index) {
          final exercise = exercises[index];
          return Padding(
            padding: EdgeInsets.only(bottom: AppTheme.spacing16),
            child: _buildExerciseCard(exercise),
          );
        },
      );
    } else {
      // Use GridView for tablet and desktop
      return GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: ResponsiveBreakpoints.isDesktop(context) ? 3 : 2,
          crossAxisSpacing: AppTheme.spacing16,
          mainAxisSpacing: AppTheme.spacing16,
          childAspectRatio: ResponsiveBreakpoints.isDesktop(context) ? 1.2 : 1.1,
        ),
        itemCount: exercises.length,
        itemBuilder: (context, index) {
          final exercise = exercises[index];
          return _buildExerciseCard(exercise);
        },
      );
    }
  }

  Widget _buildExerciseCard(Exercise exercise) {
    final color = _getExerciseColor(exercise.type);
    final isMobile = ResponsiveBreakpoints.isMobile(context);

    return GestureDetector(
      onTap: () => _navigateToExercise(exercise),
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing20),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1.w,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isMobile ? _buildMobileCardContent(exercise, color) : _buildTabletDesktopCardContent(exercise, color),
      ),
    );
  }

  Widget _buildMobileCardContent(Exercise exercise, Color color) {
    return Row(
      children: [
        Container(
          width: 60.w,
          height: 60.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
          ),
          child: Icon(
            _getExerciseIcon(exercise.type),
            color: color,
            size: 28.sp,
          ),
        ),
        SizedBox(width: AppTheme.spacing16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: ResponsiveText(
                      exercise.title,
                      style: AppTheme.titleMedium.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ),
                  if (exercise.lastScore != null)
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing8,
                        vertical: 4.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getScoreColor(exercise.lastScore!).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                      ),
                      child: ResponsiveText(
                        '${exercise.lastScore}%',
                        style: AppTheme.labelSmall.copyWith(
                          color: _getScoreColor(exercise.lastScore!),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(height: AppTheme.spacing4),
              ResponsiveText(
                exercise.description,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: AppTheme.spacing8),
              Row(
                children: [
                  _buildExerciseTag(
                    icon: Icons.timer,
                    text: '${exercise.estimatedDuration}min',
                    color: AppTheme.textTertiary,
                  ),
                  SizedBox(width: AppTheme.spacing8),
                  _buildExerciseTag(
                    icon: Icons.star,
                    text: _getDifficultyText(exercise.difficulty),
                    color: _getDifficultyColor(exercise.difficulty),
                  ),
                ],
              ),
            ],
          ),
        ),
        SizedBox(width: AppTheme.spacing12),
        Icon(
          Icons.arrow_forward_ios,
          color: color,
          size: 20.sp,
        ),
      ],
    );
  }

  Widget _buildTabletDesktopCardContent(Exercise exercise, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: Icon(
                _getExerciseIcon(exercise.type),
                color: color,
                size: 24.sp,
              ),
            ),
            const Spacer(),
            if (exercise.lastScore != null)
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing8,
                  vertical: 4.h,
                ),
                decoration: BoxDecoration(
                  color: _getScoreColor(exercise.lastScore!).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
                ),
                child: ResponsiveText(
                  '${exercise.lastScore}%',
                  style: AppTheme.labelSmall.copyWith(
                    color: _getScoreColor(exercise.lastScore!),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: AppTheme.spacing12),
        ResponsiveText(
          exercise.title,
          style: AppTheme.titleMedium.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing8),
        ResponsiveText(
          exercise.description,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.textSecondary,
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        const Spacer(),
        Row(
          children: [
            _buildExerciseTag(
              icon: Icons.timer,
              text: '${exercise.estimatedDuration}min',
              color: AppTheme.textTertiary,
            ),
            SizedBox(width: AppTheme.spacing8),
            _buildExerciseTag(
              icon: Icons.star,
              text: _getDifficultyText(exercise.difficulty),
              color: _getDifficultyColor(exercise.difficulty),
            ),
            const Spacer(),
            Icon(
              Icons.play_circle_filled,
              color: color,
              size: 32.sp,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExerciseTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 6.w,
        vertical: 2.h,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 12.sp,
          ),
          SizedBox(width: 2.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToExercise(Exercise exercise) {
    switch (exercise.id) {
      case 'word_recall':
        context.go(AppRouter.wordRecall);
        break;
      case 'number_sequence':
        context.go(AppRouter.numberSequence);
        break;
      case 'shape_matching':
        context.go(AppRouter.shapeMatching);
        break;
      case 'pattern_recognition':
        context.go(AppRouter.patternRecognition);
        break;
      case 'object_memory':
        context.go(AppRouter.objectMemory);
        break;
      default:
        // For other exercises, show coming soon
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${exercise.title} coming soon!'),
            backgroundColor: AppTheme.accentColor,
          ),
        );
        break;
    }
  }

  IconData _getExerciseIcon(String type) {
    switch (type) {
      case 'memory':
        return Icons.memory;
      case 'verbal':
        return Icons.record_voice_over;
      case 'pattern':
        return Icons.pattern;
      case 'logic':
        return Icons.calculate;
      case 'spatial':
        return Icons.rotate_right;
      default:
        return Icons.psychology;
    }
  }

  Color _getExerciseColor(String type) {
    switch (type) {
      case 'memory':
        return AppTheme.primaryColor;
      case 'verbal':
        return AppTheme.secondaryColor;
      case 'pattern':
        return AppTheme.accentColor;
      case 'logic':
        return AppTheme.warningColor;
      case 'spatial':
        return AppTheme.successColor;
      default:
        return AppTheme.primaryColor;
    }
  }

  String _getDifficultyText(int difficulty) {
    switch (difficulty) {
      case 1:
      case 2:
        return 'Easy';
      case 3:
        return 'Medium';
      case 4:
      case 5:
        return 'Hard';
      default:
        return 'Medium';
    }
  }

  Color _getDifficultyColor(int difficulty) {
    switch (difficulty) {
      case 1:
      case 2:
        return AppTheme.successColor;
      case 3:
        return AppTheme.accentColor;
      case 4:
      case 5:
        return AppTheme.errorColor;
      default:
        return AppTheme.accentColor;
    }
  }

  Color _getScoreColor(int score) {
    if (score >= 80) {
      return AppTheme.successColor;
    } else if (score >= 60) {
      return AppTheme.accentColor;
    } else {
      return AppTheme.warningColor;
    }
  }
}
