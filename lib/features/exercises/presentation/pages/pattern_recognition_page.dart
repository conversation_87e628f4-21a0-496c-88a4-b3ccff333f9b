import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/router/app_router.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';

enum PatternPhase {
  instructions,
  pattern,
  question,
  results,
}

enum PatternType {
  colorSequence,
  shapeSequence,
  sizeSequence,
}

class PatternRecognitionPage extends StatefulWidget {
  const PatternRecognitionPage({super.key});

  @override
  State<PatternRecognitionPage> createState() => _PatternRecognitionPageState();
}

class _PatternRecognitionPageState extends State<PatternRecognitionPage> {
  // Exercise state
  PatternPhase _currentPhase = PatternPhase.instructions;
  PatternType _currentPatternType = PatternType.colorSequence;
  List<PatternElement> _patternSequence = [];
  List<PatternElement> _answerOptions = [];
  PatternElement? _correctAnswer;
  PatternElement? _selectedAnswer;
  int _score = 0;
  Timer? _timer;
  int _timeRemaining = 0;
  int _currentRound = 1;
  int _totalRounds = 3;
  int _totalScore = 0;
  
  @override
  void initState() {
    super.initState();
    _generatePattern();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _generatePattern() {
    final random = Random();
    _patternSequence.clear();
    _answerOptions.clear();
    _selectedAnswer = null;
    
    // Randomly select pattern type
    _currentPatternType = PatternType.values[random.nextInt(PatternType.values.length)];
    
    switch (_currentPatternType) {
      case PatternType.colorSequence:
        _generateColorPattern();
        break;
      case PatternType.shapeSequence:
        _generateShapePattern();
        break;
      case PatternType.sizeSequence:
        _generateSizePattern();
        break;
    }
  }

  void _generateColorPattern() {
    final colors = [
      AppTheme.primaryColor,
      AppTheme.secondaryColor,
      AppTheme.accentColor,
      AppTheme.errorColor,
    ];
    
    // Create alternating color pattern
    for (int i = 0; i < 5; i++) {
      _patternSequence.add(PatternElement(
        color: colors[i % 2],
        shape: 'circle',
        size: 1.0,
      ));
    }
    
    // Correct answer
    _correctAnswer = PatternElement(
      color: colors[5 % 2],
      shape: 'circle',
      size: 1.0,
    );
    
    // Generate answer options
    _answerOptions = [
      _correctAnswer!,
      PatternElement(color: colors[2], shape: 'circle', size: 1.0),
      PatternElement(color: colors[3], shape: 'circle', size: 1.0),
      PatternElement(color: colors[(5 + 1) % 2], shape: 'circle', size: 1.0),
    ];
    _answerOptions.shuffle();
  }

  void _generateShapePattern() {
    final shapes = ['circle', 'square', 'triangle'];
    
    // Create alternating shape pattern
    for (int i = 0; i < 5; i++) {
      _patternSequence.add(PatternElement(
        color: AppTheme.primaryColor,
        shape: shapes[i % 2],
        size: 1.0,
      ));
    }
    
    // Correct answer
    _correctAnswer = PatternElement(
      color: AppTheme.primaryColor,
      shape: shapes[5 % 2],
      size: 1.0,
    );
    
    // Generate answer options
    _answerOptions = [
      _correctAnswer!,
      PatternElement(color: AppTheme.primaryColor, shape: shapes[2], size: 1.0),
      PatternElement(color: AppTheme.primaryColor, shape: shapes[(5 + 1) % 2], size: 1.0),
      PatternElement(color: AppTheme.primaryColor, shape: shapes[0], size: 1.0),
    ];
    _answerOptions.shuffle();
  }

  void _generateSizePattern() {
    final sizes = [0.6, 1.0, 1.4];
    
    // Create increasing size pattern
    for (int i = 0; i < 5; i++) {
      _patternSequence.add(PatternElement(
        color: AppTheme.accentColor,
        shape: 'circle',
        size: sizes[i % 3],
      ));
    }
    
    // Correct answer
    _correctAnswer = PatternElement(
      color: AppTheme.accentColor,
      shape: 'circle',
      size: sizes[5 % 3],
    );
    
    // Generate answer options
    _answerOptions = [
      _correctAnswer!,
      PatternElement(color: AppTheme.accentColor, shape: 'circle', size: sizes[0]),
      PatternElement(color: AppTheme.accentColor, shape: 'circle', size: sizes[1]),
      PatternElement(color: AppTheme.accentColor, shape: 'circle', size: sizes[2]),
    ];
    _answerOptions.shuffle();
  }

  void _startPattern() {
    setState(() {
      _currentPhase = PatternPhase.pattern;
      _timeRemaining = 5; // 5 seconds to view pattern
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _timeRemaining--;
      });
      
      if (_timeRemaining <= 0) {
        timer.cancel();
        _showQuestion();
      }
    });
  }

  void _showQuestion() {
    setState(() {
      _currentPhase = PatternPhase.question;
    });
  }

  void _selectAnswer(PatternElement answer) {
    setState(() {
      _selectedAnswer = answer;
    });
  }

  void _submitAnswer() {
    bool isCorrect = _selectedAnswer?.isEqual(_correctAnswer!) ?? false;
    if (isCorrect) {
      _totalScore++;
    }
    
    if (_currentRound < _totalRounds) {
      // Next round
      setState(() {
        _currentRound++;
      });
      _generatePattern();
      setState(() {
        _currentPhase = PatternPhase.pattern;
        _timeRemaining = 5;
      });
      
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeRemaining--;
        });
        
        if (_timeRemaining <= 0) {
          timer.cancel();
          _showQuestion();
        }
      });
    } else {
      // Show results
      setState(() {
        _currentPhase = PatternPhase.results;
      });
    }
  }

  void _restartExercise() {
    setState(() {
      _currentPhase = PatternPhase.instructions;
      _currentRound = 1;
      _totalScore = 0;
    });
    _generatePattern();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'Pattern Recognition',
          style: AppTheme.headlineSmall.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.accentColor.withOpacity(0.1),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop, otherwise navigate to exercises
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            } else {
              context.go(AppRouter.exercises);
            }
          },
        ),
      ),
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing16),
        child: _buildExerciseContent(),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 600.w),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: Center(
        child: Container(
          constraints: BoxConstraints(maxWidth: 800.w),
          padding: EdgeInsets.all(AppTheme.spacing32),
          child: _buildExerciseContent(),
        ),
      ),
    );
  }

  Widget _buildExerciseContent() {
    switch (_currentPhase) {
      case PatternPhase.instructions:
        return _buildInstructionsPhase();
      case PatternPhase.pattern:
        return _buildPatternPhase();
      case PatternPhase.question:
        return _buildQuestionPhase();
      case PatternPhase.results:
        return _buildResultsPhase();
    }
  }

  Widget _buildInstructionsPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            Icons.pattern,
            size: 60.sp,
            color: AppTheme.accentColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        ResponsiveText(
          'Pattern Recognition',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'Instructions',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                '1. You will see a pattern for 5 seconds\n'
                '2. Study the sequence carefully\n'
                '3. Choose what comes next in the pattern\n'
                '4. Complete 3 rounds to finish!',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _startPattern,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Start Exercise',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPatternPhase() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.primaryColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.timer,
                color: AppTheme.primaryColor,
                size: 24.sp,
              ),
              SizedBox(width: AppTheme.spacing8),
              ResponsiveText(
                'Study the pattern: $_timeRemaining seconds',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Round $_currentRound of $_totalRounds',
          style: AppTheme.titleSmall.copyWith(
            color: AppTheme.textSecondary,
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _patternSequence.map((element) {
              return _buildPatternElement(element);
            }).toList(),
          ),
        ),
        SizedBox(height: AppTheme.spacing40),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing16),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          ),
          child: ResponsiveText(
            'Look for the pattern in ${_getPatternTypeDescription()}',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildQuestionPhase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: AppTheme.accentColor.withOpacity(0.3),
              width: 1.w,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.quiz,
                color: AppTheme.accentColor,
                size: 32.sp,
              ),
              SizedBox(height: AppTheme.spacing12),
              ResponsiveText(
                'What comes next?',
                style: AppTheme.titleLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                'Round $_currentRound of $_totalRounds',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        // Show pattern with question mark
        Container(
          padding: EdgeInsets.all(AppTheme.spacing20),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
            border: Border.all(
              color: AppTheme.dividerColor,
              width: 1.w,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ..._patternSequence.map((element) => _buildPatternElement(element)),
              Container(
                width: 50.w,
                height: 50.h,
                decoration: BoxDecoration(
                  color: AppTheme.textTertiary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                  border: Border.all(
                    color: AppTheme.textTertiary,
                    width: 2.w,
                    style: BorderStyle.solid,
                  ),
                ),
                child: Center(
                  child: ResponsiveText(
                    '?',
                    style: AppTheme.headlineMedium.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppTheme.textTertiary,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Choose the correct answer:',
          style: AppTheme.titleSmall.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        Expanded(
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppTheme.spacing12,
              mainAxisSpacing: AppTheme.spacing12,
              childAspectRatio: 1.0,
            ),
            itemCount: _answerOptions.length,
            itemBuilder: (context, index) {
              final option = _answerOptions[index];
              final isSelected = _selectedAnswer?.isEqual(option) ?? false;

              return GestureDetector(
                onTap: () => _selectAnswer(option),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: EdgeInsets.all(AppTheme.spacing16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.accentColor.withOpacity(0.2)
                        : AppTheme.surfaceColor,
                    borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.accentColor
                          : AppTheme.dividerColor,
                      width: isSelected ? 3.w : 1.w,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: AppTheme.accentColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ] : null,
                  ),
                  child: Center(
                    child: _buildPatternElement(option),
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: AppTheme.spacing16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _selectedAnswer != null ? _submitAnswer : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentColor,
              padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
            ),
            child: ResponsiveText(
              'Submit Answer',
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textOnPrimary,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsPhase() {
    double percentage = (_totalScore / _totalRounds) * 100;

    Color scoreColor;
    String scoreMessage;
    IconData scoreIcon;

    if (percentage >= 80) {
      scoreColor = AppTheme.successColor;
      scoreMessage = 'Excellent pattern recognition!';
      scoreIcon = Icons.star;
    } else if (percentage >= 60) {
      scoreColor = AppTheme.accentColor;
      scoreMessage = 'Good logical thinking!';
      scoreIcon = Icons.thumb_up;
    } else {
      scoreColor = AppTheme.warningColor;
      scoreMessage = 'Keep practicing patterns!';
      scoreIcon = Icons.trending_up;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 120.w,
          height: 120.h,
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusXLarge),
          ),
          child: Icon(
            scoreIcon,
            size: 60.sp,
            color: scoreColor,
          ),
        ),
        SizedBox(height: AppTheme.spacing24),
        ResponsiveText(
          'Exercise Complete!',
          style: AppTheme.displaySmall.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimary,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppTheme.spacing16),
        Container(
          padding: EdgeInsets.all(AppTheme.spacing24),
          decoration: BoxDecoration(
            color: scoreColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppTheme.radiusLarge),
            border: Border.all(
              color: scoreColor.withOpacity(0.3),
              width: 2.w,
            ),
          ),
          child: Column(
            children: [
              ResponsiveText(
                '${percentage.toInt()}%',
                style: AppTheme.displayLarge.copyWith(
                  fontWeight: FontWeight.w800,
                  color: scoreColor,
                ),
              ),
              SizedBox(height: AppTheme.spacing8),
              ResponsiveText(
                scoreMessage,
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              SizedBox(height: AppTheme.spacing16),
              ResponsiveText(
                'You got $_totalScore out of $_totalRounds patterns correct',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        SizedBox(height: AppTheme.spacing32),
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // Navigate back to exercises page
                  context.go(AppRouter.exercises);
                },
                style: OutlinedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                  side: BorderSide(color: AppTheme.textSecondary),
                ),
                child: ResponsiveText(
                  'Back to Exercises',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppTheme.spacing16),
            Expanded(
              child: ElevatedButton(
                onPressed: _restartExercise,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentColor,
                  padding: EdgeInsets.symmetric(vertical: AppTheme.spacing16),
                ),
                child: ResponsiveText(
                  'Try Again',
                  style: AppTheme.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textOnPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPatternElement(PatternElement element) {
    return Container(
      width: 50.w,
      height: 50.h,
      decoration: BoxDecoration(
        color: element.color,
        borderRadius: element.shape == 'circle'
            ? BorderRadius.circular(25.r)
            : BorderRadius.circular(8.r),
      ),
      transform: Matrix4.identity()..scale(element.size),
      child: element.shape == 'triangle'
          ? CustomPaint(
              painter: TrianglePainter(color: element.color),
            )
          : null,
    );
  }

  String _getPatternTypeDescription() {
    switch (_currentPatternType) {
      case PatternType.colorSequence:
        return 'colors';
      case PatternType.shapeSequence:
        return 'shapes';
      case PatternType.sizeSequence:
        return 'sizes';
    }
  }
}

class PatternElement {
  final Color color;
  final String shape;
  final double size;

  PatternElement({
    required this.color,
    required this.shape,
    required this.size,
  });

  bool isEqual(PatternElement other) {
    return color == other.color &&
           shape == other.shape &&
           size == other.size;
  }
}

class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
