import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../domain/entities/exercise.dart';

part 'exercises_bloc.freezed.dart';
part 'exercises_event.dart';
part 'exercises_state.dart';

class ExercisesBloc extends Bloc<ExercisesEvent, ExercisesState> {
  ExercisesBloc() : super(const _Initial()) {
    on<ExercisesEvent>((event, emit) async {
      try {
        if (event is _LoadRequested) {
          emit(const _Loading());
          
          // Simulate API call delay
          await Future.delayed(const Duration(seconds: 1));
          
          // Mock exercises data
          final exercises = [
            const Exercise(
              id: 'word_recall',
              title: 'Word Recall Exercise',
              description: 'Remember and recall words to enhance verbal memory',
              type: 'verbal',
              difficulty: 3,
              estimatedDuration: 8,
              iconPath: 'assets/icons/word.png',
              isCompleted: true,
              lastScore: 78,
            ),
            const Exercise(
              id: 'number_sequence',
              title: 'Number Sequence Recall',
              description: 'Enhance working memory by recalling number sequences',
              type: 'logic',
              difficulty: 2,
              estimatedDuration: 6,
              iconPath: 'assets/icons/numbers.png',
              isCompleted: false,
            ),
            const Exercise(
              id: 'shape_matching',
              title: 'Shape Matching',
              description: 'Match shapes to improve visual memory and recognition',
              type: 'memory',
              difficulty: 2,
              estimatedDuration: 5,
              iconPath: 'assets/icons/shapes.png',
              isCompleted: false,
            ),
            const Exercise(
              id: 'pattern_recognition',
              title: 'Pattern Recognition',
              description: 'Identify patterns to boost cognitive processing',
              type: 'pattern',
              difficulty: 4,
              estimatedDuration: 10,
              iconPath: 'assets/icons/pattern.png',
              isCompleted: false,
            ),
            const Exercise(
              id: 'object_memory',
              title: 'Object Memory Recall',
              description: 'Remember everyday objects to strengthen visual memory',
              type: 'memory',
              difficulty: 3,
              estimatedDuration: 7,
              iconPath: 'assets/icons/objects.png',
              isCompleted: false,
            ),
          ];
          
          emit(_Loaded(
            exercises: exercises,
            filteredExercises: exercises,
            currentFilter: null,
            difficultyFilter: null,
          ));
        }
        
        if (event is _ExerciseStarted) {
          if (state is _Loaded) {
            final currentState = state as _Loaded;
            final exercise = currentState.exercises
                .firstWhere((ex) => ex.id == event.exerciseId);
            
            emit(_ExerciseInProgress(exercise));
          }
        }
        
        if (event is _ExerciseCompleted) {
          if (state is _ExerciseInProgress) {
            // Simulate saving result
            await Future.delayed(const Duration(milliseconds: 500));
            
            // Reload exercises with updated data
            add(const ExercisesEvent.loadRequested());
          }
        }
        
        if (event is _FilterChanged) {
          if (state is _Loaded) {
            final currentState = state as _Loaded;
            
            List<Exercise> filteredExercises = currentState.exercises;
            
            if (event.filterType != null) {
              filteredExercises = filteredExercises
                  .where((exercise) => exercise.type == event.filterType)
                  .toList();
            }
            
            if (event.filterDifficulty != null) {
              filteredExercises = filteredExercises
                  .where((exercise) => exercise.difficulty == event.filterDifficulty)
                  .toList();
            }
            
            emit(_Loaded(
              exercises: currentState.exercises,
              filteredExercises: filteredExercises,
              currentFilter: event.filterType,
              difficultyFilter: event.filterDifficulty,
            ));
          }
        }
      } on Exception catch (e) {
        emit(_Error(e.toString()));
      }
    });
  }
}
