part of 'exercises_bloc.dart';

@freezed
class ExercisesEvent with _$ExercisesEvent {
  const factory ExercisesEvent.loadRequested() = _LoadRequested;
  const factory ExercisesEvent.exerciseStarted(String exerciseId) = _ExerciseStarted;
  const factory ExercisesEvent.exerciseCompleted(ExerciseResult result) = _ExerciseCompleted;
  const factory ExercisesEvent.filterChanged({
    String? filterType,
    int? filterDifficulty,
  }) = _FilterChanged;
}
