import 'package:freezed_annotation/freezed_annotation.dart';

part 'exercise.freezed.dart';

@freezed
class Exercise with _$Exercise {
  const factory Exercise({
    required String id,
    required String title,
    required String description,
    required String type,
    required int difficulty, // 1-5 scale
    required int estimatedDuration, // in minutes
    required String iconPath,
    @Default(false) bool isCompleted,
    int? lastScore,
    DateTime? lastPlayedAt,
  }) = _Exercise;
}

@freezed
class ExerciseResult with _$ExerciseResult {
  const factory ExerciseResult({
    required String exerciseId,
    required int score,
    required int timeSpent, // in seconds
    required DateTime completedAt,
    required Map<String, dynamic> details,
  }) = _ExerciseResult;
}
