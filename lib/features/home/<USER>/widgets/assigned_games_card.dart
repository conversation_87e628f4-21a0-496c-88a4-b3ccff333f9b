import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../auth/data/model/assigned_game_model.dart';

class AssignedGamesCard extends StatelessWidget {
  final List<AssignedGameInfo> assignedGames;
  final bool isLoading;

  const AssignedGamesCard({
    super.key,
    required this.assignedGames,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppTheme.spacing20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.games, color: AppTheme.accentColor, size: 20.sp),
                SizedBox(width: AppTheme.spacing8),
                ResponsiveText(
                  'Assigned Games',
                  style: AppTheme.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                const Spacer(),
                if (assignedGames.isNotEmpty)
                  TextButton(
                    onPressed: () {
                      // Navigate to games page when implemented
                    },
                    child: ResponsiveText(
                      'View All',
                      style: AppTheme.labelMedium.copyWith(
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: AppTheme.spacing16),
            if (isLoading)
              _buildLoadingState()
            else if (assignedGames.isEmpty)
              _buildEmptyState()
            else
              _buildGamesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
          strokeWidth: 2.w,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.games_outlined,
              color: AppTheme.textTertiary,
              size: 32.sp,
            ),
            SizedBox(height: AppTheme.spacing8),
            ResponsiveText(
              'No assigned games',
              style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGamesList() {
    return Column(
      children: assignedGames.take(3).map((game) {
        return Padding(
          padding: EdgeInsets.only(bottom: AppTheme.spacing12),
          child: _buildGameItem(game),
        );
      }).toList(),
    );
  }

  Widget _buildGameItem(AssignedGameInfo game) {
    return GestureDetector(
      onTap: () {
        // Navigate to game when implemented
      },
      child: Container(
        padding: EdgeInsets.all(AppTheme.spacing16),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.2),
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: AppTheme.accentColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
              ),
              child: Icon(
                Icons.games,
                color: AppTheme.accentColor,
                size: 24.sp,
              ),
            ),
            SizedBox(width: AppTheme.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ResponsiveText(
                    game.gameName ?? 'Unknown Game',
                    style: AppTheme.titleSmall.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: AppTheme.spacing4),
                  ResponsiveText(
                    game.categoryName ?? 'Unknown Category',
                    style: AppTheme.bodySmall.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  SizedBox(height: AppTheme.spacing8),
                  Wrap(
                    spacing: AppTheme.spacing8,
                    children: [
                      _buildGameTag(
                        icon: Icons.layers,
                        text: '${game.noOfLevels ?? 0} levels',
                        color: AppTheme.textTertiary,
                      ),
                      if (game.isActive == true)
                        _buildGameTag(
                          icon: Icons.check_circle,
                          text: 'Active',
                          color: AppTheme.successColor,
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Icon(
              Icons.play_circle_filled,
              color: AppTheme.accentColor,
              size: 32.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameTag({
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppTheme.spacing8,
        vertical: 4.h,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12.sp, color: color),
          SizedBox(width: 4.w),
          ResponsiveText(
            text,
            style: AppTheme.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
