import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:recallloop/core/database/hive_db_helper.dart';
import 'package:recallloop/features/auth/data/model/assigned_game_model.dart';
import 'package:recallloop/features/auth/data/services/listall_assignedgames_service.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/presentation/widgets/responsive_layout.dart';
import '../../../exercises/presentation/bloc/exercises/exercises_bloc.dart';
import '../../../mood/presentation/bloc/mood/mood_bloc.dart';
import '../../../profile/presentation/bloc/profile/profile_bloc.dart';
import '../widgets/assigned_games_card.dart';
import '../widgets/home_header.dart';
import '../widgets/mood_check_in_card.dart';
import '../widgets/quick_stats_card.dart';
import '../widgets/recent_activities_card.dart';
import '../widgets/recommended_exercises_card.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String? displayName;
  bool isLoadingGames = false;
  List<AssignedGameInfo>? assignedGames;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      isLoadingGames = true;
    });

    // Load BLoC data
    context.read<ProfileBloc>().add(const ProfileEvent.loadRequested());
    context.read<ExercisesBloc>().add(const ExercisesEvent.loadRequested());
    context.read<MoodBloc>().add(const MoodEvent.loadRequested());

    try {
      final hiveUserService = HiveUserService();

      // Get user info first (this seems to be working)
      final userInfo = await hiveUserService.getUserInfo();
      print("User info from local storage: $userInfo");

      // Try to get current user with better error handling
      dynamic currentUser;
      String? accessToken;

      try {
        currentUser = await hiveUserService.getCurrentUser();
        accessToken = currentUser?.accessToken;
        print("Current user loaded successfully");
      } catch (e) {
        print("Error getting current user: $e");
        // Continue without current user data, we'll use userInfo instead
        currentUser = null;
      }

      // Extract required parameters
      // String? tenantId;
      String? tenantCode;
      String? partyId;
      String? partyTypeKey;

      if (userInfo != null) {
        // Use data from fn_get_user_info response
        // tenantId = userInfo['parent_party_id']?.toString();
        tenantCode = userInfo['tenant_code']?.toString();
        partyId = userInfo['id']?.toString();
        partyTypeKey = userInfo['party_type_key']?.toString();

        print("Using user info from local storage:");
        print("  tenantId=$tenantCode");
        print("  partyId=$partyId");
        print("  partyTypeKey=$partyTypeKey");
      } else if (currentUser != null) {
        // Fallback to current user data
        // tenantId = currentUser.tenantId?.toString();
        tenantCode = currentUser.tenantCode?.toString();
        partyId = currentUser.partyId?.toString();
        partyTypeKey = currentUser.partyTypeKey?.toString();

        print("Using current user data:");
        print("  tenantId=$tenantCode");
        print("  partyId=$partyId");
        print("  partyTypeKey=$partyTypeKey");
      }

      // Validate required parameters
      // if (tenantId?.isNotEmpty == true &&
      if (tenantCode?.isNotEmpty == true &&
          partyId?.isNotEmpty == true &&
          partyTypeKey?.isNotEmpty == true) {
        print("Loading assigned games with validated parameters...");

        final games = await AssignedGamesInfoService.getAssignedGames(
          // tenantId: tenantId!,
          tenantCode: tenantCode!,
          partyId: partyId!,
          partyTypeKey: partyTypeKey!,
          accessToken: accessToken,
        );

        setState(() {
          assignedGames = games;
          isLoadingGames = false;
        });

        if (games != null) {
          print("✅ Assigned games loaded: ${games.length} games");
        } else {
          print("⚠️ No assigned games returned from service");
        }
      } else {
        print("❌ Missing required user data:");
        // print(
        //   "  tenantId: ${tenantId?.isEmpty == true ? 'empty/null' : 'valid'}",
        // );
        print(
          "  tenantCode: ${tenantCode?.isEmpty == true ? 'empty/null' : 'valid'}",
        );
        print(
          "  partyId: ${partyId?.isEmpty == true ? 'empty/null' : 'valid'}",
        );
        print(
          "  partyTypeKey: ${partyTypeKey?.isEmpty == true ? 'empty/null' : 'valid'}",
        );

        setState(() {
          isLoadingGames = false;
          assignedGames = []; // Set empty list instead of null
        });
      }
    } catch (e, stackTrace) {
      print("❌ Error loading assigned games: $e");
      print("Stack trace: $stackTrace");

      setState(() {
        isLoadingGames = false;
        assignedGames = []; // Set empty list on error
      });

      // Optionally show a snackbar to the user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load assigned games: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(),
        tablet: _buildTabletLayout(),
        desktop: _buildDesktopLayout(),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          await _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(AppTheme.spacing16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeHeader(),
              SizedBox(height: AppTheme.spacing24),
              const QuickStatsCard(),
              SizedBox(height: AppTheme.spacing16),
              const RecommendedExercisesCard(),
              SizedBox(height: AppTheme.spacing16),
              AssignedGamesCard(
                assignedGames: assignedGames ?? [],
                isLoading: isLoadingGames,
              ),
              SizedBox(height: AppTheme.spacing16),
              const MoodCheckInCard(),
              SizedBox(height: AppTheme.spacing16),
              const RecentActivitiesCard(),
              SizedBox(height: AppTheme.spacing24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabletLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          await _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(AppTheme.spacing24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const HomeHeader(),
              SizedBox(height: AppTheme.spacing32),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        const QuickStatsCard(),
                        SizedBox(height: AppTheme.spacing16),
                        const RecommendedExercisesCard(),
                        SizedBox(height: AppTheme.spacing16),
                        AssignedGamesCard(
                          assignedGames: assignedGames ?? [],
                          isLoading: isLoadingGames,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: AppTheme.spacing16),
                  Expanded(
                    child: Column(
                      children: [
                        const MoodCheckInCard(),
                        SizedBox(height: AppTheme.spacing16),
                        const RecentActivitiesCard(),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppTheme.spacing32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          await _loadData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ResponsiveContainer(
            maxWidth: 1200.w,
            padding: EdgeInsets.all(AppTheme.spacing32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const HomeHeader(),
                SizedBox(height: AppTheme.spacing40),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 3,
                      child: Column(
                        children: [
                          const QuickStatsCard(),
                          SizedBox(height: AppTheme.spacing24),
                          const RecommendedExercisesCard(),
                          SizedBox(height: AppTheme.spacing24),
                          AssignedGamesCard(
                            assignedGames: assignedGames ?? [],
                            isLoading: isLoadingGames,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: AppTheme.spacing24),
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          const MoodCheckInCard(),
                          SizedBox(height: AppTheme.spacing24),
                          const RecentActivitiesCard(),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppTheme.spacing40),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Quick action floating button for mobile
class QuickActionButton extends StatelessWidget {
  const QuickActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        _showQuickActionSheet(context);
      },
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: AppTheme.textOnPrimary,
      icon: const Icon(Icons.add),
      label: ResponsiveText(
        'Quick Start',
        style: AppTheme.labelMedium.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }

  void _showQuickActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.surfaceColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppTheme.radiusLarge),
        ),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(AppTheme.spacing24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ResponsiveText(
              'Quick Actions',
              style: AppTheme.titleLarge.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppTheme.spacing16),
            _buildQuickActionItem(
              context,
              icon: Icons.psychology,
              title: 'Start Exercise',
              subtitle: 'Begin a cognitive exercise',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.exercises);
              },
            ),
            _buildQuickActionItem(
              context,
              icon: Icons.mood,
              title: 'Log Mood',
              subtitle: 'Track your current mood',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.moodTracking);
              },
            ),
            _buildQuickActionItem(
              context,
              icon: Icons.analytics,
              title: 'View Progress',
              subtitle: 'Check your performance',
              onTap: () {
                Navigator.pop(context);
                context.go(AppRouter.progress);
              },
            ),
            SizedBox(height: AppTheme.spacing16),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 48.w,
        height: 48.h,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppTheme.radiusMedium),
        ),
        child: Icon(icon, color: AppTheme.primaryColor, size: 24.sp),
      ),
      title: ResponsiveText(
        title,
        style: AppTheme.titleMedium.copyWith(fontWeight: FontWeight.w600),
      ),
      subtitle: ResponsiveText(
        subtitle,
        style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }
}
